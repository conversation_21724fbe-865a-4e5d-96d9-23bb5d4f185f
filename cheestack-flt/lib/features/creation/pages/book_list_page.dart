import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/shared/utils/refresh_controller_factory.dart';

import '../controllers/creation_controller.dart';
import 'widgets/book_filter_bar.dart';
import 'widgets/book_card.dart';
import 'widgets/enhanced_search_bar.dart';

/// 书籍管理列表页面
class BookListPage extends StatefulWidget {
  const BookListPage({super.key});

  @override
  State<BookListPage> createState() => _BookListPageState();
}

class _BookListPageState extends State<BookListPage>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  late CreationController controller;
  late RefreshController refreshController;
  late String _controllerKey;
  late ScrollController _scrollController;
  late TextEditingController _searchController;
  late AnimationController _fabAnimationController;

  bool _showBackToTop = false;
  bool _isLoading = true;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _controllerKey = 'book_list_${hashCode}';
    refreshController = RefreshControllerFactory.create(key: _controllerKey);
    _scrollController = ScrollController();
    _searchController = TextEditingController();
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 监听滚动事件
    _scrollController.addListener(_onScroll);

    if (!Get.isRegistered<CreationController>()) {
      Get.put<CreationController>(CreationController());
    }
    controller = Get.find<CreationController>();

    // 异步加载数据
    _loadInitialData();
  }

  void _onScroll() {
    final showBackToTop = _scrollController.offset > 200;
    if (showBackToTop != _showBackToTop) {
      setState(() {
        _showBackToTop = showBackToTop;
      });
      if (_showBackToTop) {
        _fabAnimationController.forward();
      } else {
        _fabAnimationController.reverse();
      }
    }
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await controller.loadBookList();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    RefreshControllerFactory.dispose(_controllerKey);
    _scrollController.dispose();
    _searchController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<CreationController>(
      builder: (ctrl) {
        return Scaffold(
          appBar: _buildAppBar(ctrl),
          body: Column(
            children: [
              // 移除BookFilterBar，只保留工具栏和同步状态
              _buildSimplifiedToolbar(ctrl),
              _buildSyncStatusBar(ctrl),
              Expanded(child: _buildView(ctrl)),
            ],
          ),
          floatingActionButton: _buildFloatingActionButton(ctrl),
        );
      },
    );
  }

  AppBar _buildAppBar(CreationController ctrl) {
    return AppBar(
      title: EnhancedSearchBar(
        controller: _searchController,
        hintText: '搜索书籍名称、简介、作者...',
        onChanged: _onSearchChanged,
        onSubmitted: (value) => ctrl.onBookSearchChanged(value),
        searchHistory: _getSearchHistory(),
        searchSuggestions: _getSearchSuggestions(),
        onHistoryTap: () => _showSearchHistory(context),
      ),
      // 移除筛选按钮和活跃筛选栏，简化AppBar
    );
  }

  /// 构建简化的工具栏 - 包含排序、筛选、视图切换按钮
  Widget _buildSimplifiedToolbar(CreationController ctrl) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // 主工具栏
          Row(
            children: [
              _buildResultCount(context, ctrl),
              const Spacer(),
              _buildSortButton(context, ctrl),
              SizedBox(width: 8.w),
              _buildFilterButton(context, ctrl),
              SizedBox(width: 8.w),
              _buildViewToggle(context, ctrl),
            ],
          ),
          // 活跃筛选标签（如果有的话）
          if (ctrl.isFilterActive) ...[
            SizedBox(height: 8.h),
            _buildActiveFiltersRow(ctrl),
          ],
        ],
      ),
    );
  }

  /// 构建结果统计
  Widget _buildResultCount(BuildContext context, CreationController ctrl) {
    final count = ctrl.filteredBookList.length;
    return OxText(
      '共 $count 本书籍',
      fontSize: AppTheme.fontSmall,
      color: Theme.of(context).colorScheme.onSurfaceVariant,
    );
  }

  /// 构建排序按钮
  Widget _buildSortButton(BuildContext context, CreationController ctrl) {
    return GestureDetector(
      onTap: () => _showSortOptions(context, ctrl),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.sort,
              size: 16.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(width: 4.w),
            OxText(
              '排序',
              fontSize: AppTheme.fontSmall,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建筛选按钮
  Widget _buildFilterButton(BuildContext context, CreationController ctrl) {
    return GestureDetector(
      onTap: () => _showFilterPanel(context, ctrl),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: ctrl.isFilterActive
              ? Theme.of(context).colorScheme.primaryContainer
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: ctrl.isFilterActive
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.5)
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.tune,
              size: 16.sp,
              color: ctrl.isFilterActive
                  ? Theme.of(context).colorScheme.onPrimaryContainer
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(width: 4.w),
            OxText(
              '筛选',
              fontSize: AppTheme.fontSmall,
              color: ctrl.isFilterActive
                  ? Theme.of(context).colorScheme.onPrimaryContainer
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建视图切换按钮
  Widget _buildViewToggle(BuildContext context, CreationController ctrl) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildViewButton(
            context,
            icon: Icons.view_list,
            isSelected: !ctrl.isGridView,
            onTap: () {
              if (ctrl.isGridView) {
                ctrl.toggleViewMode();
              }
            },
          ),
          _buildViewButton(
            context,
            icon: Icons.grid_view,
            isSelected: ctrl.isGridView,
            onTap: () {
              if (!ctrl.isGridView) {
                ctrl.toggleViewMode();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildViewButton(
    BuildContext context, {
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Icon(
          icon,
          size: 16.sp,
          color: isSelected
              ? Theme.of(context).colorScheme.onPrimary
              : Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  /// 构建活跃筛选标签行
  Widget _buildActiveFiltersRow(CreationController ctrl) {
    final activeFilters = <String>[];

    if (ctrl.selectedPrivacyFilter != 'all') {
      activeFilters.add(ctrl.selectedPrivacyFilter == 'public' ? '公开' : '私有');
    }
    if (ctrl.selectedTimeFilter != 'all') {
      final timeLabels = {
        'today': '今天',
        'week': '本周',
        'month': '本月',
      };
      activeFilters.add(timeLabels[ctrl.selectedTimeFilter] ?? '');
    }

    if (activeFilters.isEmpty) return const SizedBox.shrink();

    return Row(
      children: [
        Expanded(
          child: Wrap(
            spacing: 8.w,
            children: activeFilters.asMap().entries.map((entry) {
              final index = entry.key;
              final filter = entry.value;
              return Chip(
                label: OxText(
                  filter,
                  fontSize: AppTheme.fontSmall,
                ),
                deleteIcon: Icon(Icons.close, size: 14.sp),
                onDeleted: () => _removeFilter(ctrl, index),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              );
            }).toList(),
          ),
        ),
        TextButton(
          onPressed: () => ctrl.clearAllFilters(),
          child: OxText(
            '清除全部',
            fontSize: AppTheme.fontSmall,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  /// 构建同步状态栏 - 简化版本
  Widget _buildSyncStatusBar(CreationController ctrl) {
    // 只在同步进行中或有错误时显示
    if (!ctrl.isSyncing && ctrl.syncStatus != 'failed') {
      return const SizedBox.shrink();
    }

    return Container(
      height: 32.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: _getSyncStatusBarColor(ctrl),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          if (ctrl.isSyncing)
            SizedBox(
              width: 16.sp,
              height: 16.sp,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            )
          else
            Icon(
              Icons.error_outline,
              size: 16.sp,
              color: Theme.of(context).colorScheme.error,
            ),
          SizedBox(width: 8.w),
          Expanded(
            child: OxText(
              ctrl.isSyncing ? '正在同步...' : '同步失败',
              fontSize: AppTheme.fontSmall,
              color: _getSyncStatusTextColor(ctrl),
              fontWeight: FontWeight.w500,
            ),
          ),
          if (ctrl.syncStatus == 'failed')
            GestureDetector(
              onTap: () => ctrl.syncBooksOnly(),
              child: OxText(
                '重试',
                fontSize: AppTheme.fontSmall,
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          SizedBox(width: 8.w),
          GestureDetector(
            onTap: () => ctrl.resetSyncStatus(),
            child: Icon(
              Icons.close,
              size: 16.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取同步状态栏颜色
  Color _getSyncStatusBarColor(CreationController ctrl) {
    if (ctrl.isSyncing) {
      return Theme.of(context)
          .colorScheme
          .primaryContainer
          .withValues(alpha: 0.3);
    }
    if (ctrl.syncStatus == 'failed') {
      return Theme.of(context)
          .colorScheme
          .errorContainer
          .withValues(alpha: 0.3);
    }
    return Theme.of(context).colorScheme.surface;
  }



  /// 获取同步状态文本颜色
  Color _getSyncStatusTextColor(CreationController ctrl) {
    if (ctrl.isSyncing) {
      return Theme.of(context).colorScheme.primary;
    }
    if (ctrl.syncStatus == 'failed') {
      return Theme.of(context).colorScheme.error;
    }
    return Theme.of(context).colorScheme.onSurface;
  }

  Widget _buildView(CreationController ctrl) {
    if (_isLoading) {
      return _buildSkeletonView();
    }

    if (ctrl.filteredBookList.isEmpty) {
      return ctrl.bookList.isEmpty ? _buildEmptyView() : _buildNoResultsView();
    }
    return _buildBookList(ctrl);
  }

  Widget _buildSkeletonView() {
    return ListView.builder(
      padding: AppTheme.paddingMedium,
      itemCount: 6,
      itemBuilder: (context, index) {
        return Card(
          margin: EdgeInsets.only(bottom: AppTheme.spacingSmall),
          child: Padding(
            padding: AppTheme.paddingMedium,
            child: Row(
              children: [
                Container(
                  width: 60.w,
                  height: 80.h,
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 16.h,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Container(
                        height: 12.h,
                        width: 0.7.sw,
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Container(
                        height: 12.h,
                        width: 0.5.sw,
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Padding(
        padding: AppTheme.paddingLarge,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120.w,
              height: 120.h,
              decoration: BoxDecoration(
                color: Theme.of(context)
                    .colorScheme
                    .primaryContainer
                    .withValues(alpha: 0.3),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.auto_stories,
                size: 64.sp,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 24.h),
            OxText(
              '开始您的知识之旅',
              fontSize: AppTheme.fontLarge,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            SizedBox(height: 12.h),
            OxText(
              '创建您的第一本书籍，记录和分享知识',
              fontSize: AppTheme.fontBody,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            ElevatedButton.icon(
              onPressed: () => controller.createBook(),
              icon: const Icon(Icons.add),
              label: const OxText('创建第一本书籍'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              ),
            ),
            SizedBox(height: 16.h),
            TextButton.icon(
              onPressed: () => controller.syncBooksOnly(),
              icon: const Icon(Icons.cloud_download),
              label: const OxText('从云端同步'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoResultsView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          OxText(
            '没有找到匹配的书籍',
            fontSize: AppTheme.fontTitle,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          OxText(
            '尝试调整筛选条件或搜索关键词',
            fontSize: AppTheme.fontSmall,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBookList(CreationController ctrl) {
    return Stack(
      children: [
        SmartRefresher(
          controller: refreshController,
          onRefresh: _onRefresh,
          onLoading: _onLoadMore,
          enablePullUp: true,
          enablePullDown: true,
          child: ctrl.isGridView ? _buildGridView(ctrl) : _buildListView(ctrl),
        ),
        // 返回顶部按钮
        if (_showBackToTop)
          Positioned(
            right: 16.w,
            bottom: 80.h, // 避免与FAB重叠
            child: AnimatedBuilder(
              animation: _fabAnimationController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _fabAnimationController.value,
                  child: FloatingActionButton.small(
                    onPressed: _scrollToTop,
                    heroTag: "backToTop",
                    child: const Icon(Icons.keyboard_arrow_up),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  Widget _buildListView(CreationController ctrl) {
    return ListView.builder(
      controller: _scrollController,
      padding: AppTheme.paddingMedium,
      itemCount: ctrl.filteredBookList.length,
      itemBuilder: (context, index) {
        final book = ctrl.filteredBookList[index];
        return BookListCard(
          book: book,
          controller: ctrl,
        );
      },
    );
  }

  Widget _buildGridView(CreationController ctrl) {
    return GridView.builder(
      controller: _scrollController,
      padding: AppTheme.paddingMedium,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _getGridCrossAxisCount(),
        childAspectRatio: 0.65, // 调整宽高比，提供更多垂直空间
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
      ),
      itemCount: ctrl.filteredBookList.length,
      itemBuilder: (context, index) {
        final book = ctrl.filteredBookList[index];
        return BookGridCard(
          book: book,
          controller: ctrl,
        );
      },
    );
  }

  int _getGridCrossAxisCount() {
    final screenWidth = 1.sw;
    if (screenWidth > 600.w) {
      return 3; // 平板设备显示3列
    } else {
      return 2; // 手机设备显示2列
    }
  }

  Future<void> _onRefresh() async {
    try {
      await controller.loadBookList();
      refreshController.refreshCompleted();
    } catch (e) {
      refreshController.refreshFailed();
    }
  }

  Future<void> _onLoadMore() async {
    try {
      final moreBooks = await controller.loadMoreBooks();

      if (moreBooks.isEmpty) {
        refreshController.loadNoData();
      } else {
        controller.bookList.addAll(moreBooks);
        controller.update();
        refreshController.loadComplete();
      }
    } catch (e) {
      refreshController.loadFailed();
    }
  }

  Widget _buildFloatingActionButton(CreationController ctrl) {
    return FloatingActionButton(
      onPressed: () => ctrl.createBook(),
      child: const Icon(Icons.add),
    );
  }

  void _showFilterPanel(BuildContext context, CreationController ctrl) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => BookFilterPanel(controller: ctrl),
    );
  }

  /// 显示排序选项
  void _showSortOptions(BuildContext context, CreationController ctrl) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _SortOptionsSheet(controller: ctrl),
    );
  }

  // 搜索相关方法
  void _onSearchChanged(String value) {
    // 防抖处理，避免频繁请求
    Future.delayed(const Duration(milliseconds: 300), () {
      if (_searchController.text == value) {
        controller.onBookSearchChanged(value);
        _saveSearchHistory(value);
      }
    });
  }

  List<String> _getSearchHistory() {
    // 这里应该从本地存储获取搜索历史
    // 暂时返回模拟数据
    return ['Flutter开发', 'Dart语言', '移动应用'];
  }

  List<String> _getSearchSuggestions() {
    // 这里应该根据当前输入获取搜索建议
    // 暂时返回模拟数据
    return ['编程入门', '技术文档', '学习笔记'];
  }

  void _showSearchHistory(BuildContext context) {
    // 显示搜索历史对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const OxText('搜索历史'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _getSearchHistory()
              .map(
                (history) => ListTile(
                  title: OxText(history),
                  onTap: () {
                    _searchController.text = history;
                    controller.onBookSearchChanged(history);
                    Navigator.pop(context);
                  },
                ),
              )
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const OxText('关闭'),
          ),
        ],
      ),
    );
  }

  void _saveSearchHistory(String keyword) {
    if (keyword.trim().isEmpty) return;
    // 这里应该保存搜索历史到本地存储
    // 暂时省略实现
  }

  void _removeFilter(CreationController ctrl, int index) {
    // 根据索引移除对应的筛选条件
    if (index == 0 && ctrl.selectedPrivacyFilter != 'all') {
      ctrl.setPrivacyFilter('all');
    } else if (ctrl.selectedTimeFilter != 'all') {
      ctrl.setTimeFilter('all');
    }
  }
}

/// 排序选项底部弹窗 - 优化设计
class _SortOptionsSheet extends StatelessWidget {
  final CreationController controller;

  const _SortOptionsSheet({required this.controller});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CreationController>(
      builder: (ctrl) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7, // 响应式高度限制
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 固定头部
              Padding(
                padding: EdgeInsets.all(24.w),
                child: _buildHeader(context),
              ),

              // 可滚动的选项列表
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ..._buildSortOptions(context, ctrl),
                    ],
                  ),
                ),
              ),

              // 底部间距
              SizedBox(height: 24.h),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        OxText(
          '排序方式',
          fontSize: AppTheme.fontTitle,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.onSurface,
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Get.back(),
          icon: Icon(
            Icons.close,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  List<Widget> _buildSortOptions(
      BuildContext context, CreationController ctrl) {
    final sortOptions = [
      {
        'value': 'created_desc',
        'label': '创建时间（最新优先）',
        'icon': Icons.access_time
      },
      {
        'value': 'created_asc',
        'label': '创建时间（最早优先）',
        'icon': Icons.access_time
      },
      {'value': 'updated_desc', 'label': '更新时间（最新优先）', 'icon': Icons.update},
      {'value': 'updated_asc', 'label': '更新时间（最早优先）', 'icon': Icons.update},
      {'value': 'name_asc', 'label': '名称（A-Z）', 'icon': Icons.sort_by_alpha},
      {'value': 'name_desc', 'label': '名称（Z-A）', 'icon': Icons.sort_by_alpha},
    ];

    return sortOptions.map((option) {
      final isSelected = ctrl.selectedSortOption == option['value'];
      return Container(
        margin: EdgeInsets.only(bottom: 12.h),
        child: InkWell(
          onTap: () {
            ctrl.setSortOption(option['value'] as String);
            Get.back();
          },
          borderRadius: BorderRadius.circular(12.r),
          child: Container(
            constraints: BoxConstraints(
              minHeight: 56.h, // 确保最小触摸目标
            ),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(context).colorScheme.primaryContainer
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context)
                        .colorScheme
                        .outline
                        .withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  option['icon'] as IconData,
                  color: isSelected
                      ? Theme.of(context).colorScheme.onPrimaryContainer
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 20.sp,
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: OxText(
                    option['label'] as String,
                    fontSize: AppTheme.fontBody,
                    color: isSelected
                        ? Theme.of(context).colorScheme.onPrimaryContainer
                        : Theme.of(context).colorScheme.onSurface,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (isSelected) ...[
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.check_circle,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20.sp,
                  ),
                ],
              ],
            ),
          ),
        ),
      );
    }).toList();
  }
}
