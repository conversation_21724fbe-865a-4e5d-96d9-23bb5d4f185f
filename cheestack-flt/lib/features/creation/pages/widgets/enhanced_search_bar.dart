import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 增强版搜索栏组件
/// 支持实时搜索、搜索历史、搜索建议和清空功能
class EnhancedSearchBar extends StatefulWidget {
  final String? hintText;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final VoidCallback? onClear;
  final TextEditingController? controller;
  final List<String>? searchHistory;
  final List<String>? searchSuggestions;
  final bool showHistory;
  final bool showSuggestions;
  final VoidCallback? onHistoryTap;

  const EnhancedSearchBar({
    super.key,
    this.hintText,
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.controller,
    this.searchHistory,
    this.searchSuggestions,
    this.showHistory = true,
    this.showSuggestions = true,
    this.onHistoryTap,
  });

  @override
  State<EnhancedSearchBar> createState() => _EnhancedSearchBarState();
}

class _EnhancedSearchBarState extends State<EnhancedSearchBar> {
  late TextEditingController _controller;
  bool _showClearButton = false;
  bool _showDropdown = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
    _showClearButton = _controller.text.isNotEmpty;
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.isNotEmpty;
    if (hasText != _showClearButton) {
      setState(() {
        _showClearButton = hasText;
      });
    }
    widget.onChanged?.call(_controller.text);
  }

  void _onFocusChanged() {
    setState(() {
      _showDropdown = _focusNode.hasFocus &&
          (widget.searchHistory?.isNotEmpty == true ||
              widget.searchSuggestions?.isNotEmpty == true);
    });
  }

  void _onClear() {
    _controller.clear();
    widget.onClear?.call();
  }

  void _onSuggestionTap(String suggestion) {
    _controller.text = suggestion;
    _focusNode.unfocus();
    widget.onSubmitted?.call(suggestion);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: _showDropdown ? 140.h : 40.h, // 减少动态高度，防止溢出
      child: Stack(
        children: [
          // 搜索输入框
          Container(
            height: 40.h,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(20.r),
              border: _focusNode.hasFocus
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 1,
                    )
                  : Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .outline
                          .withValues(alpha: 0.3),
                      width: 1,
                    ),
            ),
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              onSubmitted: widget.onSubmitted,
              decoration: InputDecoration(
                hintText: widget.hintText ?? '搜索书籍、卡片...',
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  size: 18.sp,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                suffixIcon: _showClearButton
                    ? IconButton(
                        onPressed: _onClear,
                        icon: Icon(
                          Icons.clear,
                          size: 16.sp,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 8.h,
                ),
              ),
              style: TextStyle(
                fontSize: 14.sp,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),

          // 搜索建议下拉框 - 使用Positioned确保正确位置
          if (_showDropdown)
            Positioned(
              top: 44.h, // 搜索框下方4px
              left: 0,
              right: 0,
              child: _buildDropdown(context),
            ),
        ],
      ),
    );
  }

  /// 构建搜索建议下拉框 - 修复布局溢出问题
  Widget _buildDropdown(BuildContext context) {
    final hasHistory = widget.searchHistory?.isNotEmpty == true;
    final hasSuggestions = widget.searchSuggestions?.isNotEmpty == true;

    if (!hasHistory && !hasSuggestions) return const SizedBox.shrink();

    return Container(
      constraints: BoxConstraints(
        maxHeight: 100.h, // 进一步减少最大高度，防止溢出
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 搜索历史 - 简化显示
            if (hasHistory && widget.showHistory) ...[
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                child: Text(
                  '最近搜索',
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
              ...widget.searchHistory!.take(1).map((history) =>
                  _buildDropdownItem(context, history, Icons.history)),
            ],

            // 搜索建议 - 简化显示
            if (hasSuggestions && widget.showSuggestions) ...[
              if (hasHistory)
                Divider(
                  height: 1,
                  color: Theme.of(context)
                      .colorScheme
                      .outline
                      .withValues(alpha: 0.2),
                ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                child: Text(
                  '搜索建议',
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
              ...widget.searchSuggestions!.take(1).map((suggestion) =>
                  _buildDropdownItem(context, suggestion, Icons.search)),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建下拉框项目 - 优化布局
  Widget _buildDropdownItem(BuildContext context, String text, IconData icon) {
    return InkWell(
      onTap: () => _onSuggestionTap(text),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        child: Row(
          children: [
            Icon(
              icon,
              size: 14.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
