import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/widgets/index.dart';
import '../../controllers/creation_controller.dart';

/// 书籍筛选栏组件
class BookFilterBar extends StatelessWidget {
  final CreationController controller;
  final bool isVisible;

  const BookFilterBar({
    super.key,
    required this.controller,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Container(
      padding: AppTheme.paddingMedium,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildQuickFilters(context),
          if (controller.isFilterActive) ...[
            _buildActiveFilters(context),
          ],
        ],
      ),
    );
  }

  /// 构建快速筛选标签
  Widget _buildQuickFilters(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildFilterChip(
            context,
            label: '全部',
            isSelected: !controller.isFilterActive,
            onTap: () => controller.clearAllFilters(),
          ),
          SizedBox(width: 8.w),
          _buildFilterChip(
            context,
            label: '公开',
            isSelected: controller.selectedPrivacyFilter == 'public',
            onTap: () => controller.setPrivacyFilter('public'),
          ),
          SizedBox(width: 8.w),
          _buildFilterChip(
            context,
            label: '私有',
            isSelected: controller.selectedPrivacyFilter == 'private',
            onTap: () => controller.setPrivacyFilter('private'),
          ),
          SizedBox(width: 8.w),
          _buildFilterChip(
            context,
            label: '今天',
            isSelected: controller.selectedTimeFilter == 'today',
            onTap: () => controller.setTimeFilter('today'),
          ),
          SizedBox(width: 8.w),
          _buildFilterChip(
            context,
            label: '本周',
            isSelected: controller.selectedTimeFilter == 'week',
            onTap: () => controller.setTimeFilter('week'),
          ),
          SizedBox(width: 8.w),
          _buildFilterChip(
            context,
            label: '本月',
            isSelected: controller.selectedTimeFilter == 'month',
            onTap: () => controller.setTimeFilter('month'),
          ),
        ],
      ),
    );
  }

  /// 构建筛选标签
  Widget _buildFilterChip(
    BuildContext context, {
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        child: OxText(
          label,
          fontSize: AppTheme.fontSmall,
          color: isSelected
              ? Theme.of(context).colorScheme.onPrimary
              : Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  /// 构建当前激活的筛选条件
  Widget _buildActiveFilters(BuildContext context) {
    List<Widget> activeFilters = [];

    if (controller.selectedPrivacyFilter != 'all') {
      activeFilters.add(_buildActiveFilterChip(
        context,
        label:
            controller.getPrivacyFilterText(controller.selectedPrivacyFilter),
        onRemove: () => controller.setPrivacyFilter('all'),
      ));
    }

    if (controller.selectedTimeFilter != 'all') {
      activeFilters.add(_buildActiveFilterChip(
        context,
        label: controller.getTimeFilterText(controller.selectedTimeFilter),
        onRemove: () => controller.setTimeFilter('all'),
      ));
    }

    if (controller.bookSearchKeyword.isNotEmpty) {
      activeFilters.add(_buildActiveFilterChip(
        context,
        label: '搜索: ${controller.bookSearchKeyword}',
        onRemove: () => controller.onBookSearchChanged(''),
      ));
    }

    if (activeFilters.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            OxText(
              '当前筛选:',
              fontSize: AppTheme.fontSmall,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const Spacer(),
            GestureDetector(
              onTap: controller.clearAllFilters,
              child: OxText(
                '清除全部',
                fontSize: AppTheme.fontSmall,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 4.h,
          children: activeFilters,
        ),
      ],
    );
  }

  /// 构建激活的筛选条件标签
  Widget _buildActiveFilterChip(
    BuildContext context, {
    required String label,
    required VoidCallback onRemove,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          OxText(
            label,
            fontSize: AppTheme.fontSmall,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
          SizedBox(width: 4.w),
          GestureDetector(
            onTap: onRemove,
            child: Icon(
              Icons.close,
              size: 14.sp,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
        ],
      ),
    );
  }
}

/// 筛选面板组件
class BookFilterPanel extends StatelessWidget {
  final CreationController controller;

  const BookFilterPanel({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CreationController>(
      builder: (ctrl) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.6, // 响应式高度限制
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
            boxShadow: [
              BoxShadow(
                color:
                    Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 固定头部
              Padding(
                padding: AppTheme.paddingMedium,
                child: _buildHeader(context),
              ),

              // 可滚动的内容区域
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(
                      horizontal: AppTheme.paddingMedium.left),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 20.h),
                      _buildPrivacySection(context, ctrl),
                      SizedBox(height: 20.h),
                      _buildTimeSection(context, ctrl),
                      SizedBox(height: 20.h),
                    ],
                  ),
                ),
              ),

              // 固定底部操作按钮
              Padding(
                padding: AppTheme.paddingMedium,
                child: _buildActions(context, ctrl),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        OxText(
          '筛选和排序',
          fontSize: AppTheme.fontTitle,
          fontWeight: FontWeight.bold,
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Get.back(),
          icon: Icon(
            Icons.close,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildPrivacySection(BuildContext context, CreationController ctrl) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        OxText(
          '隐私设置',
          fontSize: AppTheme.fontBody,
          fontWeight: FontWeight.w600,
        ),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 12.w,
          runSpacing: 12.h,
          children: ['all', 'public', 'private'].map((filter) {
            return _buildFilterOption(
              context,
              label: ctrl.getPrivacyFilterText(filter),
              isSelected: ctrl.selectedPrivacyFilter == filter,
              onTap: () => ctrl.setPrivacyFilter(filter),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildTimeSection(BuildContext context, CreationController ctrl) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        OxText(
          '创建时间',
          fontSize: AppTheme.fontBody,
          fontWeight: FontWeight.w600,
        ),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 12.w,
          runSpacing: 12.h,
          children: ['all', 'today', 'week', 'month'].map((filter) {
            return _buildFilterOption(
              context,
              label: ctrl.getTimeFilterText(filter),
              isSelected: ctrl.selectedTimeFilter == filter,
              onTap: () => ctrl.setTimeFilter(filter),
            );
          }).toList(),
        ),
      ],
    );
  }



  Widget _buildFilterOption(
    BuildContext context, {
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        child: OxText(
          label,
          fontSize: AppTheme.fontBody,
          color: isSelected
              ? Theme.of(context).colorScheme.onPrimary
              : Theme.of(context).colorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildActions(BuildContext context, CreationController ctrl) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: ctrl.clearAllFilters,
            child: const OxText('重置'),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: ElevatedButton(
            onPressed: () => Get.back(),
            child: const OxText('确定'),
          ),
        ),
      ],
    );
  }
}
