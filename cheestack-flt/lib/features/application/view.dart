part of application_page;

class ApplicationPage extends StatefulWidget {
  const ApplicationPage({Key? key}) : super(key: key);

  @override
  State<ApplicationPage> createState() => _ApplicationPageState();
}

class _ApplicationPageState extends State<ApplicationPage>
    with WidgetsBindingObserver, LifecycleAware, LifecycleMixin {
  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    FlutterLifecycleDetector().onBackgroundChange.listen((isBackground) {
      Console.log("isBackground: $isBackground");
      // if (!isBackground) {
      //   Sads().showSplashAd();
      // }
    });
    super.initState();
  }

  @override
  void didChangePlatformBrightness() {
    super.didChangePlatformBrightness();
    AppTheme.setSystemStyle();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ApplicationController>(
      init: ApplicationController(),
      builder: (controller) => Scaffold(
        body: PageView(
          physics: const NeverScrollableScrollPhysics(),
          controller: controller.pageController,
          onPageChanged: controller.onPageChanged,
          children: const [
            StudyPage(),
            CreationHomePage(),
            ListeningPage(),
            ProfilePage(),
            // HomePage(),

          ],
        ),
        bottomNavigationBar: GetBuilder<ApplicationController>(
          builder: (controller) => BottomNavigationBar(
            currentIndex: controller.currentPage,
            onTap: (page) {
              controller.pageController.jumpToPage(page);
            },
            items: [
              
              BottomNavigationBarItem(
                icon: const Icon(Ionicons.school_outline),
                // 学习图标
                activeIcon: const Icon(Ionicons.school),
                label: LocaleKeys.navStudy.tr,
              ),
              BottomNavigationBarItem(
                // 创作
                icon: const Icon(Ionicons.create_outline),
                activeIcon: const Icon(Ionicons.create),
                label: LocaleKeys.navCreation.tr,
              ),
              BottomNavigationBarItem(
                icon: const Icon(Ionicons.headset),
                // 学习图标
                activeIcon: const Icon(Ionicons.headset),
                label: LocaleKeys.navListening.tr,
              ),
              BottomNavigationBarItem(
                icon: const Icon(Ionicons.person_outline),
                activeIcon: const Icon(Ionicons.person),
                label: LocaleKeys.navProfile.tr,
              ),
              // BottomNavigationBarItem(
              //   icon: const Icon(Ionicons.home_outline),
              //   activeIcon: const Icon(Ionicons.home),
              //   label: LocaleKeys.navHome.tr,
              // ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void onLifecycleEvent(LifecycleEvent event) {
    if (event == LifecycleEvent.active) {
      ApiConfig.get();
      SupgradeController.to.checkUpdate();
    }
  }
}
