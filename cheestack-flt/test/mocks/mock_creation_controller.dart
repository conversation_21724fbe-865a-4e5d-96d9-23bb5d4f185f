import 'package:mockito/mockito.dart';
import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';
import 'package:cheestack_flt/models/index.dart';

class MockCreationController extends Mock implements CreationController {
  @override
  List<BookModel> get filteredBookList => super.noSuchMethod(
        Invocation.getter(#filteredBookList),
        returnValue: <BookModel>[],
      );

  @override
  List<BookModel> get bookList => super.noSuchMethod(
        Invocation.getter(#bookList),
        returnValue: <BookModel>[],
      );

  @override
  bool get isFilterActive => super.noSuchMethod(
        Invocation.getter(#isFilterActive),
        returnValue: false,
      );

  @override
  String get bookSearchKeyword => super.noSuchMethod(
        Invocation.getter(#bookSearchKeyword),
        returnValue: '',
      );

  @override
  bool get isSyncing => super.noSuchMethod(
        Invocation.getter(#isSyncing),
        returnValue: false,
      );

  @override
  String get syncStatus => super.noSuchMethod(
        Invocation.getter(#syncStatus),
        returnValue: 'idle',
      );

  @override
  bool get isGridView => super.noSuchMethod(
        Invocation.getter(#isGridView),
        returnValue: false,
      );

  @override
  String get selectedPrivacyFilter => super.noSuchMethod(
        Invocation.getter(#selectedPrivacyFilter),
        returnValue: 'all',
      );

  @override
  String get selectedTimeFilter => super.noSuchMethod(
        Invocation.getter(#selectedTimeFilter),
        returnValue: 'all',
      );

  @override
  String get selectedSortOption => super.noSuchMethod(
        Invocation.getter(#selectedSortOption),
        returnValue: 'created_desc',
      );

  @override
  void onBookSearchChanged(String keyword) => super.noSuchMethod(
        Invocation.method(#onBookSearchChanged, [keyword]),
        returnValueForMissingStub: null,
      );

  @override
  Future<void> loadBookList() => super.noSuchMethod(
        Invocation.method(#loadBookList, []),
        returnValue: Future<void>.value(),
      );

  @override
  void toggleViewMode() => super.noSuchMethod(
        Invocation.method(#toggleViewMode, []),
        returnValueForMissingStub: null,
      );

  @override
  void setPrivacyFilter(String filter) => super.noSuchMethod(
        Invocation.method(#setPrivacyFilter, [filter]),
        returnValueForMissingStub: null,
      );

  @override
  void setTimeFilter(String filter) => super.noSuchMethod(
        Invocation.method(#setTimeFilter, [filter]),
        returnValueForMissingStub: null,
      );

  @override
  void setSortOption(String option) => super.noSuchMethod(
        Invocation.method(#setSortOption, [option]),
        returnValueForMissingStub: null,
      );

  @override
  void clearAllFilters() => super.noSuchMethod(
        Invocation.method(#clearAllFilters, []),
        returnValueForMissingStub: null,
      );

  @override
  Future<void> createBook() => super.noSuchMethod(
        Invocation.method(#createBook, []),
        returnValue: Future<void>.value(),
      );

  @override
  String getSortOptionText(String option) => super.noSuchMethod(
        Invocation.method(#getSortOptionText, [option]),
        returnValue: '创建时间（最新优先）',
      );

  @override
  void update([List<Object>? ids, bool condition = true]) => super.noSuchMethod(
        Invocation.method(#update, [ids, condition]),
        returnValueForMissingStub: null,
      );
}
