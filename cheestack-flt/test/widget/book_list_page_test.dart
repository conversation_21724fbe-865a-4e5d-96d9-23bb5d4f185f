import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';

import 'package:cheestack_flt/features/creation/pages/book_list_page.dart';
import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';
import 'package:cheestack_flt/models/index.dart';

import '../mocks/mock_creation_controller.dart';

void main() {
  group('BookListPage UI 布局测试', () {
    late MockCreationController mockController;

    setUp(() {
      mockController = MockCreationController();
      Get.put<CreationController>(mockController);
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('搜索框点击时不应该导致布局溢出', (WidgetTester tester) async {
      // Arrange
      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);
      when(mockController.bookSearchKeyword).thenReturn('');
      when(mockController.isSyncing).thenReturn(false);
      when(mockController.syncStatus).thenReturn('idle');

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // 点击搜索框
      final searchField = find.byType(TextField);
      expect(searchField, findsOneWidget);
      
      await tester.tap(searchField);
      await tester.pump();

      // Assert - 检查是否有溢出错误
      expect(tester.takeException(), isNull);
    });

    testWidgets('搜索历史下拉框应该在搜索框下方正确显示', (WidgetTester tester) async {
      // Arrange
      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);
      when(mockController.bookSearchKeyword).thenReturn('');

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // 点击搜索框触发历史显示
      final searchField = find.byType(TextField);
      await tester.tap(searchField);
      await tester.pump();

      // Assert - 搜索历史应该在搜索框下方
      // 这里需要检查具体的布局位置
      expect(tester.takeException(), isNull);
    });

    testWidgets('筛选面板布局应该有合适的行距', (WidgetTester tester) async {
      // Arrange
      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);
      when(mockController.bookSearchKeyword).thenReturn('');

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // 点击筛选按钮
      final filterButton = find.byIcon(Icons.tune);
      await tester.tap(filterButton);
      await tester.pumpAndSettle();

      // Assert - 检查筛选面板是否正确显示
      expect(find.text('筛选和排序'), findsOneWidget);
      expect(tester.takeException(), isNull);
    });

    testWidgets('工具栏应该只显示必要的按钮', (WidgetTester tester) async {
      // Arrange
      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);
      when(mockController.bookSearchKeyword).thenReturn('');
      when(mockController.isGridView).thenReturn(false);

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // Assert - 工具栏应该包含排序、筛选、视图切换按钮，但不包含同步按钮
      expect(find.byIcon(Icons.sort), findsOneWidget);
      expect(find.byIcon(Icons.view_list), findsOneWidget);
      expect(find.byIcon(Icons.grid_view), findsOneWidget);
      // 同步按钮应该被移除
      expect(find.byIcon(Icons.sync), findsNothing);
    });

    testWidgets('悬浮按钮应该只显示图标不显示文字', (WidgetTester tester) async {
      // Arrange
      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // Assert - 悬浮按钮应该是简单的图标按钮
      expect(find.byType(FloatingActionButton), findsOneWidget);
      expect(find.text('创建书籍'), findsNothing);
    });
  });

  group('BookListPage 功能测试', () {
    late MockCreationController mockController;

    setUp(() {
      mockController = MockCreationController();
      Get.put<CreationController>(mockController);
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('筛选后应该只显示活跃筛选标签', (WidgetTester tester) async {
      // Arrange
      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(true);
      when(mockController.selectedPrivacyFilter).thenReturn('public');
      when(mockController.selectedTimeFilter).thenReturn('today');
      when(mockController.bookSearchKeyword).thenReturn('');

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // Assert - 应该只显示活跃筛选标签，不显示重复内容
      expect(find.text('公开'), findsOneWidget);
      expect(find.text('今天'), findsOneWidget);
      expect(find.text('当前筛选'), findsNothing);
    });
  });
}
