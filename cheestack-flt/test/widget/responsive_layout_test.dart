import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';

import 'package:cheestack_flt/features/creation/pages/book_list_page.dart';
import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';

import '../mocks/mock_creation_controller.dart';

void main() {
  group('响应式布局测试', () {
    late MockCreationController mockController;

    setUp(() {
      mockController = MockCreationController();
      Get.put<CreationController>(mockController);
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('排序弹窗在小屏幕上不应该溢出', (WidgetTester tester) async {
      // Arrange - 模拟小屏幕设备
      await tester.binding.setSurfaceSize(const Size(375, 667)); // iPhone SE尺寸

      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);
      when(mockController.bookSearchKeyword).thenReturn('');
      when(mockController.selectedSortOption).thenReturn('created_desc');
      // 移除有问题的mock调用，测试主要关注布局不溢出

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // 点击排序按钮
      final sortButton = find.text('排序');
      expect(sortButton, findsOneWidget);

      await tester.tap(sortButton);
      await tester.pumpAndSettle();

      // Assert - 检查是否有溢出错误
      expect(tester.takeException(), isNull);

      // 验证排序弹窗正确显示
      expect(find.text('排序方式'), findsOneWidget);
      expect(find.text('创建时间（最新优先）'), findsOneWidget);
    });

    testWidgets('筛选面板在小屏幕上不应该溢出', (WidgetTester tester) async {
      // Arrange - 模拟小屏幕设备
      await tester.binding.setSurfaceSize(const Size(375, 667));

      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);
      when(mockController.bookSearchKeyword).thenReturn('');
      when(mockController.selectedPrivacyFilter).thenReturn('all');
      when(mockController.selectedTimeFilter).thenReturn('all');
      // 移除有问题的mock调用，测试主要关注布局不溢出

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // 点击筛选按钮
      final filterButton = find.text('筛选');
      expect(filterButton, findsOneWidget);

      await tester.tap(filterButton);
      await tester.pumpAndSettle();

      // Assert - 检查是否有溢出错误
      expect(tester.takeException(), isNull);

      // 验证筛选面板正确显示
      expect(find.text('筛选和排序'), findsOneWidget);
      expect(find.text('隐私设置'), findsOneWidget);
      expect(find.text('创建时间'), findsOneWidget);
    });

    testWidgets('搜索下拉框在小屏幕上不应该溢出', (WidgetTester tester) async {
      // Arrange - 模拟小屏幕设备
      await tester.binding.setSurfaceSize(const Size(375, 667));

      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);
      when(mockController.bookSearchKeyword).thenReturn('');

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // 点击搜索框
      final searchField = find.byType(TextField);
      expect(searchField, findsOneWidget);

      await tester.tap(searchField);
      await tester.pump();

      // Assert - 检查是否有溢出错误
      expect(tester.takeException(), isNull);
    });

    testWidgets('大屏幕设备上布局应该正常', (WidgetTester tester) async {
      // Arrange - 模拟大屏幕设备
      await tester.binding.setSurfaceSize(const Size(768, 1024)); // iPad尺寸

      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);
      when(mockController.bookSearchKeyword).thenReturn('');

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // Assert - 检查是否有溢出错误
      expect(tester.takeException(), isNull);

      // 验证主要组件都正确显示
      expect(find.byType(TextField), findsOneWidget); // 搜索框
      expect(find.text('排序'), findsOneWidget); // 排序按钮
      expect(find.text('筛选'), findsOneWidget); // 筛选按钮
      expect(find.byType(FloatingActionButton), findsOneWidget); // 悬浮按钮
    });
  });
}
