import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';

import 'package:cheestack_flt/features/creation/pages/book_list_page.dart';
import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';

import '../mocks/mock_creation_controller.dart';

void main() {
  group('搜索栏布局优化测试', () {
    late MockCreationController mockController;

    setUp(() {
      mockController = MockCreationController();
      Get.put<CreationController>(mockController);
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('AppBar返回按钮应该占用最小空间', (WidgetTester tester) async {
      // Arrange
      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);
      when(mockController.bookSearchKeyword).thenReturn('');
      when(mockController.isGridView).thenReturn(false);

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // Assert - 检查AppBar布局
      final appBar = find.byType(AppBar);
      expect(appBar, findsOneWidget);
      
      // 搜索栏应该占据大部分空间
      final searchField = find.byType(TextField);
      expect(searchField, findsOneWidget);
    });

    testWidgets('搜索栏右侧应该只有清除按钮', (WidgetTester tester) async {
      // Arrange
      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);
      when(mockController.bookSearchKeyword).thenReturn('test');

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // 输入文字到搜索框
      await tester.enterText(find.byType(TextField), 'test');
      await tester.pump();

      // Assert - 应该只有清除按钮，没有历史按钮
      expect(find.byIcon(Icons.clear), findsOneWidget);
      expect(find.byIcon(Icons.history), findsNothing);
    });

    testWidgets('点击搜索栏应该显示小窗口而不是底部弹窗', (WidgetTester tester) async {
      // Arrange
      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);
      when(mockController.bookSearchKeyword).thenReturn('');

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // 点击搜索框
      await tester.tap(find.byType(TextField));
      await tester.pump();

      // Assert - 检查是否有溢出错误
      expect(tester.takeException(), isNull);
    });

    testWidgets('搜索历史窗口应该与搜索栏等宽', (WidgetTester tester) async {
      // Arrange
      when(mockController.filteredBookList).thenReturn([]);
      when(mockController.bookList).thenReturn([]);
      when(mockController.isFilterActive).thenReturn(false);
      when(mockController.bookSearchKeyword).thenReturn('');

      // Act
      await tester.pumpWidget(
        ScreenUtilInit(
          designSize: const Size(375, 812),
          child: GetMaterialApp(
            home: const BookListPage(),
          ),
        ),
      );

      // 点击搜索框触发历史显示
      await tester.tap(find.byType(TextField));
      await tester.pump();

      // Assert - 检查布局是否稳定
      expect(tester.takeException(), isNull);
    });
  });
}
